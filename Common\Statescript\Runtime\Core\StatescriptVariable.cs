using System;
using System.Collections.Generic;
using System.Linq;
using MemoryPack;

namespace GP.Common.Statescript
{
    /// <summary>
    /// Variable types supported by the Statescript system
    /// </summary>
    public enum StatescriptVariableType
    {
        <PERSON>olean,
        Integer,
        Float,
        String,
        Vector2,
        Vector3,
        Entity,
        Object
    }

    /// <summary>
    /// Base class for all Statescript variables using discriminated union pattern
    /// </summary>
    [MemoryPackable]
    [MemoryPackUnion(0, typeof(BooleanVariable))]
    [MemoryPackUnion(1, typeof(IntegerVariable))]
    [MemoryPackUnion(2, typeof(FloatVariable))]
    [MemoryPackUnion(3, typeof(StringVariable))]
    [MemoryPackUnion(4, typeof(Vector2Variable))]
    [MemoryPackUnion(5, typeof(Vector3Variable))]
    [MemoryPackUnion(6, typeof(EntityVariable))]
    [MemoryPackUnion(7, typeof(ObjectVariable))]
    public abstract partial class StatescriptVariable
    {
        [MemoryPackInclude]
        public string Name { get; set; } = string.Empty;

        [MemoryPackInclude]
        public string Description { get; set; } = string.Empty;

        [MemoryPackInclude]
        public bool IsExposed { get; set; } = false;

        /// <summary>
        /// Get the type of this variable
        /// </summary>
        public abstract StatescriptVariableType Type { get; }

        /// <summary>
        /// Get the value as a specific type without boxing (when possible)
        /// </summary>
        public abstract T GetValue<T>(T fallback = default);

        /// <summary>
        /// Get the value as an object (for editor/debugging purposes)
        /// </summary>
        public abstract object GetValueAsObject();

        /// <summary>
        /// Set the value with type checking
        /// </summary>
        public abstract bool SetValue(object value);

        /// <summary>
        /// Reset to default value for this variable type
        /// </summary>
        public abstract void ResetToDefault();

        /// <summary>
        /// Validate the variable configuration
        /// </summary>
        public virtual List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrEmpty(Name))
            {
                errors.Add("Variable must have a name");
            }

            return errors;
        }

        [MemoryPackConstructor]
        protected StatescriptVariable()
        {
        }

        protected StatescriptVariable(string name)
        {
            Name = name;
        }

        public override string ToString()
        {
            var value = GetValueAsObject();
            return $"{Name} ({Type}): {value}";
        }
    }

    /// <summary>
    /// Boolean variable implementation
    /// </summary>
    [MemoryPackable]
    public sealed partial class BooleanVariable : StatescriptVariable
    {
        [MemoryPackInclude]
        public bool Value { get; set; }

        public override StatescriptVariableType Type => StatescriptVariableType.Boolean;

        [MemoryPackConstructor]
        public BooleanVariable() : base()
        {
        }

        public BooleanVariable(string name, bool value = false) : base(name)
        {
            Value = value;
        }

        public override T GetValue<T>(T fallback = default)
        {
            if (typeof(T) == typeof(bool))
                return (T)(object)Value;

            // Try conversion
            try
            {
                return (T)Convert.ChangeType(Value, typeof(T));
            }
            catch
            {
                return fallback;
            }
        }

        public override object GetValueAsObject() => Value;

        public override bool SetValue(object value)
        {
            switch (value)
            {
                case bool boolVal:
                    Value = boolVal;
                    return true;
                case string stringVal when bool.TryParse(stringVal, out var parsed):
                    Value = parsed;
                    return true;
                case int intVal:
                    Value = intVal != 0;
                    return true;
                default:
                    return false;
            }
        }

        public bool SetValue(bool value)
        {
            Value = value;
            return true;
        }

        public override void ResetToDefault()
        {
            Value = false;
        }
    }

    /// <summary>
    /// Integer variable implementation
    /// </summary>
    [MemoryPackable]
    public sealed partial class IntegerVariable : StatescriptVariable
    {
        [MemoryPackInclude]
        public int Value { get; set; }

        public override StatescriptVariableType Type => StatescriptVariableType.Integer;

        [MemoryPackConstructor]
        public IntegerVariable() : base()
        {
        }

        public IntegerVariable(string name, int value = 0) : base(name)
        {
            Value = value;
        }

        public override T GetValue<T>(T fallback = default)
        {
            if (typeof(T) == typeof(int))
                return (T)(object)Value;

            try
            {
                return (T)Convert.ChangeType(Value, typeof(T));
            }
            catch
            {
                return fallback;
            }
        }

        public override object GetValueAsObject() => Value;

        public override bool SetValue(object value)
        {
            switch (value)
            {
                case int intVal:
                    Value = intVal;
                    return true;
                case float floatVal:
                    Value = (int)floatVal;
                    return true;
                case string stringVal when int.TryParse(stringVal, out var parsed):
                    Value = parsed;
                    return true;
                default:
                    return false;
            }
        }

        public bool SetValue(int value)
        {
            Value = value;
            return true;
        }

        public override void ResetToDefault()
        {
            Value = 0;
        }
    }

    /// <summary>
    /// Float variable implementation
    /// </summary>
    [MemoryPackable]
    public sealed partial class FloatVariable : StatescriptVariable
    {
        [MemoryPackInclude]
        public float Value { get; set; }

        public override StatescriptVariableType Type => StatescriptVariableType.Float;

        [MemoryPackConstructor]
        public FloatVariable() : base()
        {
        }

        public FloatVariable(string name, float value = 0f) : base(name)
        {
            Value = value;
        }

        public override T GetValue<T>(T fallback = default)
        {
            if (typeof(T) == typeof(float))
                return (T)(object)Value;

            try
            {
                return (T)Convert.ChangeType(Value, typeof(T));
            }
            catch
            {
                return fallback;
            }
        }

        public override object GetValueAsObject() => Value;

        public override bool SetValue(object value)
        {
            switch (value)
            {
                case float floatVal:
                    Value = floatVal;
                    return true;
                case int intVal:
                    Value = intVal;
                    return true;
                case string stringVal when float.TryParse(stringVal, out var parsed):
                    Value = parsed;
                    return true;
                default:
                    return false;
            }
        }

        public bool SetValue(float value)
        {
            Value = value;
            return true;
        }

        public override void ResetToDefault()
        {
            Value = 0f;
        }
    }

    /// <summary>
    /// String variable implementation
    /// </summary>
    [MemoryPackable]
    public sealed partial class StringVariable : StatescriptVariable
    {
        [MemoryPackInclude]
        public string Value { get; set; } = string.Empty;

        public override StatescriptVariableType Type => StatescriptVariableType.String;

        [MemoryPackConstructor]
        public StringVariable() : base()
        {
        }

        public StringVariable(string name, string value = "") : base(name)
        {
            Value = value ?? string.Empty;
        }

        public override T GetValue<T>(T fallback = default)
        {
            if (typeof(T) == typeof(string))
                return (T)(object)Value;

            try
            {
                return (T)Convert.ChangeType(Value, typeof(T));
            }
            catch
            {
                return fallback;
            }
        }

        public override object GetValueAsObject() => Value;

        public override bool SetValue(object value)
        {
            Value = value?.ToString() ?? string.Empty;
            return true;
        }

        public bool SetValue(string value)
        {
            Value = value ?? string.Empty;
            return true;
        }

        public override void ResetToDefault()
        {
            Value = string.Empty;
        }
    }

    /// <summary>
    /// Vector2 variable implementation
    /// </summary>
    [MemoryPackable]
    public sealed partial class Vector2Variable : StatescriptVariable
    {
        [MemoryPackInclude]
        public FVector2 Value { get; set; }

        public override StatescriptVariableType Type => StatescriptVariableType.Vector2;

        [MemoryPackConstructor]
        public Vector2Variable() : base()
        {
        }

        public Vector2Variable(string name, FVector2 value = default) : base(name)
        {
            Value = value;
        }

        public override T GetValue<T>(T fallback = default)
        {
            if (typeof(T) == typeof(FVector2))
                return (T)(object)Value;

            return fallback;
        }

        public override object GetValueAsObject() => Value;

        public override bool SetValue(object value)
        {
            switch (value)
            {
                case FVector2 vec2Val:
                    Value = vec2Val;
                    return true;
                default:
                    return false;
            }
        }

        public bool SetValue(FVector2 value)
        {
            Value = value;
            return true;
        }

        public override void ResetToDefault()
        {
            Value = FVector2.zero;
        }
    }

    /// <summary>
    /// Vector3 variable implementation
    /// </summary>
    [MemoryPackable]
    public sealed partial class Vector3Variable : StatescriptVariable
    {
        [MemoryPackInclude]
        public FVector3 Value { get; set; }

        public override StatescriptVariableType Type => StatescriptVariableType.Vector3;

        [MemoryPackConstructor]
        public Vector3Variable() : base()
        {
        }

        public Vector3Variable(string name, FVector3 value = default) : base(name)
        {
            Value = value;
        }

        public override T GetValue<T>(T fallback = default)
        {
            if (typeof(T) == typeof(FVector3))
                return (T)(object)Value;

            return fallback;
        }

        public override object GetValueAsObject() => Value;

        public override bool SetValue(object value)
        {
            switch (value)
            {
                case FVector3 vec3Val:
                    Value = vec3Val;
                    return true;
                default:
                    return false;
            }
        }

        public bool SetValue(FVector3 value)
        {
            Value = value;
            return true;
        }

        public override void ResetToDefault()
        {
            Value = FVector3.zero;
        }
    }

    /// <summary>
    /// Entity variable implementation (stores entity ID)
    /// </summary>
    [MemoryPackable]
    public sealed partial class EntityVariable : StatescriptVariable
    {
        [MemoryPackInclude]
        public int EntityId { get; set; }

        public override StatescriptVariableType Type => StatescriptVariableType.Entity;

        [MemoryPackConstructor]
        public EntityVariable() : base()
        {
        }

        public EntityVariable(string name, int entityId = 0) : base(name)
        {
            EntityId = entityId;
        }

        public override T GetValue<T>(T fallback = default)
        {
            if (typeof(T) == typeof(int))
                return (T)(object)EntityId;

            return fallback;
        }

        public override object GetValueAsObject() => EntityId;

        public override bool SetValue(object value)
        {
            switch (value)
            {
                case int intVal:
                    EntityId = intVal;
                    return true;
                default:
                    return false;
            }
        }

        public bool SetValue(int entityId)
        {
            EntityId = entityId;
            return true;
        }

        public override void ResetToDefault()
        {
            EntityId = 0;
        }
    }

    /// <summary>
    /// Object variable implementation (stores as string)
    /// </summary>
    [MemoryPackable]
    public sealed partial class ObjectVariable : StatescriptVariable
    {
        [MemoryPackInclude]
        public string SerializedValue { get; set; } = string.Empty;

        public override StatescriptVariableType Type => StatescriptVariableType.Object;

        [MemoryPackConstructor]
        public ObjectVariable() : base()
        {
        }

        public ObjectVariable(string name, object value = null) : base(name)
        {
            SerializedValue = value?.ToString() ?? string.Empty;
        }

        public override T GetValue<T>(T fallback = default)
        {
            if (typeof(T) == typeof(string))
                return (T)(object)SerializedValue;

            return fallback;
        }

        public override object GetValueAsObject() => SerializedValue;

        public override bool SetValue(object value)
        {
            SerializedValue = value?.ToString() ?? string.Empty;
            return true;
        }

        public bool SetValue(string value)
        {
            SerializedValue = value ?? string.Empty;
            return true;
        }

        public override void ResetToDefault()
        {
            SerializedValue = string.Empty;
        }
    }

    /// <summary>
    /// Factory for creating Statescript variables with type safety
    /// </summary>
    public static class StatescriptVariableFactory
    {
        private static readonly Dictionary<StatescriptVariableType, Func<string, StatescriptVariable>> _factories = new()
        {
            [StatescriptVariableType.Boolean] = name => new BooleanVariable(name),
            [StatescriptVariableType.Integer] = name => new IntegerVariable(name),
            [StatescriptVariableType.Float] = name => new FloatVariable(name),
            [StatescriptVariableType.String] = name => new StringVariable(name),
            [StatescriptVariableType.Vector2] = name => new Vector2Variable(name),
            [StatescriptVariableType.Vector3] = name => new Vector3Variable(name),
            [StatescriptVariableType.Entity] = name => new EntityVariable(name),
            [StatescriptVariableType.Object] = name => new ObjectVariable(name)
        };

        /// <summary>
        /// Create a variable of the specified type
        /// </summary>
        public static StatescriptVariable Create(string name, StatescriptVariableType type)
        {
            return _factories.TryGetValue(type, out var factory)
                ? factory(name)
                : throw new ArgumentException($"Unknown variable type: {type}");
        }

        /// <summary>
        /// Create a variable with a specific value
        /// </summary>
        public static StatescriptVariable Create(string name, StatescriptVariableType type, object value)
        {
            var variable = Create(name, type);
            variable.SetValue(value);
            return variable;
        }

        /// <summary>
        /// Create a typed variable using generics
        /// </summary>
        public static T Create<T>(string name) where T : StatescriptVariable, new()
        {
            return new T { Name = name };
        }

        /// <summary>
        /// Create a boolean variable
        /// </summary>
        public static BooleanVariable CreateBoolean(string name, bool value = false)
        {
            return new BooleanVariable(name, value);
        }

        /// <summary>
        /// Create an integer variable
        /// </summary>
        public static IntegerVariable CreateInteger(string name, int value = 0)
        {
            return new IntegerVariable(name, value);
        }

        /// <summary>
        /// Create a float variable
        /// </summary>
        public static FloatVariable CreateFloat(string name, float value = 0f)
        {
            return new FloatVariable(name, value);
        }

        /// <summary>
        /// Create a string variable
        /// </summary>
        public static StringVariable CreateString(string name, string value = "")
        {
            return new StringVariable(name, value);
        }

        /// <summary>
        /// Create a Vector2 variable
        /// </summary>
        public static Vector2Variable CreateVector2(string name, FVector2 value = default)
        {
            return new Vector2Variable(name, value);
        }

        /// <summary>
        /// Create a Vector3 variable
        /// </summary>
        public static Vector3Variable CreateVector3(string name, FVector3 value = default)
        {
            return new Vector3Variable(name, value);
        }

        /// <summary>
        /// Create an entity variable
        /// </summary>
        public static EntityVariable CreateEntity(string name, int entityId = 0)
        {
            return new EntityVariable(name, entityId);
        }

        /// <summary>
        /// Create an object variable
        /// </summary>
        public static ObjectVariable CreateObject(string name, object value = null)
        {
            return new ObjectVariable(name, value);
        }

        /// <summary>
        /// Get all supported variable types
        /// </summary>
        public static StatescriptVariableType[] GetSupportedTypes()
        {
            return _factories.Keys.ToArray();
        }

        /// <summary>
        /// Check if a variable type is supported
        /// </summary>
        public static bool IsTypeSupported(StatescriptVariableType type)
        {
            return _factories.ContainsKey(type);
        }
    }
}

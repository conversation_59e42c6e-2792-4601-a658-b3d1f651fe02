#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using UnityEngine;
using GP.Common.Statescript;

namespace GP.Common.Statescript.Editor
{
    /// <summary>
    /// Editor-only data for Statescript graphs
    /// This data is not serialized to runtime and is only used in the editor
    /// </summary>
    [Serializable]
    public class StatescriptEditorData
    {
        [SerializeField]
        public List<NodeEditorData> NodeData = new();
        
        [SerializeField]
        public List<ConnectionEditorData> ConnectionData = new();
        
        [SerializeField]
        public Vector2 PanOffset = Vector2.zero;
        
        [SerializeField]
        public float ZoomLevel = 1.0f;
        
        [SerializeField]
        public Vector2 GridOffset = Vector2.zero;
        
        [SerializeField]
        public bool ShowGrid = true;
        
        [SerializeField]
        public bool ShowMinimap = false;
        
        [SerializeField]
        public string LastEditedBy = "";
        
        [SerializeField]
        public DateTime LastEditTime;

        /// <summary>
        /// Get editor data for a specific node
        /// </summary>
        public NodeEditorData GetNodeData(int nodeId)
        {
            return NodeData.Find(n => n.NodeId == nodeId);
        }

        /// <summary>
        /// Set editor data for a specific node
        /// </summary>
        public void SetNodeData(int nodeId, NodeEditorData data)
        {
            var existing = GetNodeData(nodeId);
            if (existing != null)
            {
                NodeData.Remove(existing);
            }
            data.NodeId = nodeId;
            NodeData.Add(data);
        }

        /// <summary>
        /// Remove editor data for a node
        /// </summary>
        public void RemoveNodeData(int nodeId)
        {
            NodeData.RemoveAll(n => n.NodeId == nodeId);
        }

        /// <summary>
        /// Get editor data for a specific connection
        /// </summary>
        public ConnectionEditorData GetConnectionData(int connectionId)
        {
            return ConnectionData.Find(c => c.ConnectionId == connectionId);
        }

        /// <summary>
        /// Set editor data for a specific connection
        /// </summary>
        public void SetConnectionData(int connectionId, ConnectionEditorData data)
        {
            var existing = GetConnectionData(connectionId);
            if (existing != null)
            {
                ConnectionData.Remove(existing);
            }
            data.ConnectionId = connectionId;
            ConnectionData.Add(data);
        }

        /// <summary>
        /// Remove editor data for a connection
        /// </summary>
        public void RemoveConnectionData(int connectionId)
        {
            ConnectionData.RemoveAll(c => c.ConnectionId == connectionId);
        }

        /// <summary>
        /// Clean up orphaned editor data
        /// </summary>
        public void CleanupOrphanedData(StatescriptGraph graph)
        {
            // Remove node data for nodes that no longer exist
            var validNodeIds = new HashSet<int>();
            foreach (var node in graph.Nodes)
            {
                validNodeIds.Add(node.Id);
            }
            NodeData.RemoveAll(n => !validNodeIds.Contains(n.NodeId));

            // Remove connection data for connections that no longer exist
            var validConnectionIds = new HashSet<int>();
            foreach (var connection in graph.Connections)
            {
                validConnectionIds.Add(connection.Id);
            }
            ConnectionData.RemoveAll(c => !validConnectionIds.Contains(c.ConnectionId));
        }
    }

    /// <summary>
    /// Editor-only data for individual nodes
    /// </summary>
    [Serializable]
    public class NodeEditorData
    {
        [SerializeField]
        public int NodeId;
        
        [SerializeField]
        public Vector2 Position = Vector2.zero;
        
        [SerializeField]
        public Vector2 Size = new Vector2(120, 60);
        
        [SerializeField]
        public Color Color = Color.white;
        
        [SerializeField]
        public bool IsCollapsed = false;
        
        [SerializeField]
        public bool IsSelected = false;
        
        [SerializeField]
        public string Comment = "";
        
        [SerializeField]
        public bool ShowComment = false;
        
        [SerializeField]
        public List<string> Tags = new();

        /// <summary>
        /// Get the rect for this node in editor space
        /// </summary>
        public Rect GetRect()
        {
            return new Rect(Position.x, Position.y, Size.x, Size.y);
        }

        /// <summary>
        /// Set the position of this node
        /// </summary>
        public void SetPosition(Vector2 newPosition)
        {
            Position = newPosition;
        }

        /// <summary>
        /// Check if a point is inside this node
        /// </summary>
        public bool Contains(Vector2 point)
        {
            return GetRect().Contains(point);
        }
    }

    /// <summary>
    /// Editor-only data for connections
    /// </summary>
    [Serializable]
    public class ConnectionEditorData
    {
        [SerializeField]
        public int ConnectionId;
        
        [SerializeField]
        public Color Color = Color.white;
        
        [SerializeField]
        public float Width = 2.0f;
        
        [SerializeField]
        public bool IsSelected = false;
        
        [SerializeField]
        public List<Vector2> ControlPoints = new();
        
        [SerializeField]
        public string Label = "";
        
        [SerializeField]
        public bool ShowLabel = false;

        /// <summary>
        /// Get the bezier curve points for this connection
        /// </summary>
        public Vector2[] GetBezierPoints(Vector2 startPos, Vector2 endPos)
        {
            if (ControlPoints.Count >= 2)
            {
                return new Vector2[] { startPos, ControlPoints[0], ControlPoints[1], endPos };
            }
            else
            {
                // Auto-generate control points
                var distance = Vector2.Distance(startPos, endPos);
                var offset = Mathf.Min(distance * 0.5f, 100f);
                
                var startControl = startPos + Vector2.right * offset;
                var endControl = endPos + Vector2.left * offset;
                
                return new Vector2[] { startPos, startControl, endControl, endPos };
            }
        }
    }

    /// <summary>
    /// Editor preferences for the Statescript editor
    /// </summary>
    [Serializable]
    public class StatescriptEditorPreferences
    {
        [SerializeField]
        public Color GridColor = new Color(0.3f, 0.3f, 0.3f, 0.5f);
        
        [SerializeField]
        public Color BackgroundColor = new Color(0.2f, 0.2f, 0.2f, 1.0f);
        
        [SerializeField]
        public Color SelectionColor = new Color(0.3f, 0.6f, 1.0f, 1.0f);
        
        [SerializeField]
        public float GridSize = 20f;
        
        [SerializeField]
        public bool SnapToGrid = true;
        
        [SerializeField]
        public float MinZoom = 0.1f;
        
        [SerializeField]
        public float MaxZoom = 2.0f;
        
        [SerializeField]
        public float ZoomSpeed = 0.1f;
        
        [SerializeField]
        public bool AutoSave = true;
        
        [SerializeField]
        public float AutoSaveInterval = 30f; // seconds

        /// <summary>
        /// Get default node colors for different node types
        /// </summary>
        public Color GetDefaultNodeColor(StatescriptNodeType nodeType)
        {
            return nodeType switch
            {
                StatescriptNodeType.Entry => new Color(0.2f, 0.8f, 0.2f, 1.0f),      // Green
                StatescriptNodeType.Action => new Color(0.2f, 0.6f, 1.0f, 1.0f),     // Blue
                StatescriptNodeType.Condition => new Color(1.0f, 0.8f, 0.2f, 1.0f),  // Yellow
                StatescriptNodeType.Event => new Color(1.0f, 0.4f, 0.2f, 1.0f),      // Orange
                StatescriptNodeType.Variable => new Color(0.8f, 0.2f, 1.0f, 1.0f),   // Purple
                StatescriptNodeType.FlowControl => new Color(0.6f, 0.6f, 0.6f, 1.0f), // Gray
                StatescriptNodeType.Custom => new Color(1.0f, 0.2f, 0.8f, 1.0f),     // Pink
                _ => Color.white
            };
        }

        /// <summary>
        /// Snap a position to the grid if enabled
        /// </summary>
        public Vector2 SnapToGridIfEnabled(Vector2 position)
        {
            if (!SnapToGrid) return position;
            
            return new Vector2(
                Mathf.Round(position.x / GridSize) * GridSize,
                Mathf.Round(position.y / GridSize) * GridSize
            );
        }
    }
}

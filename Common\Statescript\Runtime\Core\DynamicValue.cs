using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices; // Add this using directive
using MemoryPack;

namespace GP.Common.Statescript
{
    public enum eValueType : byte
    {
        Unknown = 0, Byte, SByte, Int16, UInt16, Int32, UInt32, Int64, UInt64,
        Float, Double, String, Boolean,
        Object
    }

    [MemoryPackable]
    public partial struct DynamicValue : IComparable, IComparable<DynamicValue>, IEquatable<DynamicValue>//, IConvertible, IFormattable
    {
        [MemoryPackInclude]
        private eValueType m_Type;
        public eValueType Type => m_Type;

        [MemoryPackInclude]
        private byte[] m_Bytes;

        [MemoryPackConstructor]
        public DynamicValue(eValueType m_Type, byte[] m_Bytes)
        {
            this.m_Type = m_Type;
            this.m_Bytes = m_Bytes ?? Array.Empty<byte>();
        }

        #region Type-sepecific Constructors
        public DynamicValue(byte value)
            : this(eValueType.Byte, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(sbyte value)
            : this(eValueType.SByte, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(short value)
            : this(eValueType.Int16, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(ushort value)
            : this(eValueType.UInt16, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(uint value)
            : this(eValueType.UInt32, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(int value)
            : this(eValueType.Int32, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(long value)
            : this(eValueType.Int64, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(ulong value)
            : this(eValueType.UInt64, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(float value)
            : this(eValueType.Float, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(double value)
            : this(eValueType.Double, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(string value)
            : this(eValueType.String, MemoryPackSerializer.Serialize(value))
        {
        }

        public DynamicValue(bool value)
            : this(eValueType.Boolean, MemoryPackSerializer.Serialize(value))
        {
        }
        #endregion

        #region Value Properties
        public byte byteValue
        {
            get => GetValueSafe<byte>(eValueType.Byte);
            set => SetValueSafe(value, eValueType.Byte);
        }

        public sbyte sbyteValue
        {
            get => GetValueSafe<sbyte>(eValueType.SByte);
            set => SetValueSafe(value, eValueType.SByte);
        }

        public short shortValue
        {
            get => GetValueSafe<short>(eValueType.Int16);
            set => SetValueSafe(value, eValueType.Int16);
        }

        public ushort ushortValue
        {
            get => GetValueSafe<ushort>(eValueType.UInt16);
            set => SetValueSafe(value, eValueType.UInt16);
        }

        public int intValue
        {
            get => GetValueSafe<int>(eValueType.Int32);
            set => SetValueSafe(value, eValueType.Int32);
        }

        public uint uintValue
        {
            get => GetValueSafe<uint>(eValueType.UInt32);
            set => SetValueSafe(value, eValueType.UInt32);
        }

        public long longValue
        {
            get => GetValueSafe<long>(eValueType.Int64);
            set => SetValueSafe(value, eValueType.Int64);
        }

        public ulong ulongValue
        {
            get => GetValueSafe<ulong>(eValueType.UInt64);
            set => SetValueSafe(value, eValueType.UInt64);
        }

        public float floatValue
        {
            get => GetValueSafe<float>(eValueType.Float);
            set => SetValueSafe(value, eValueType.Float);
        }

        public double doubleValue
        {
            get => GetValueSafe<double>(eValueType.Double);
            set => SetValueSafe(value, eValueType.Double);
        }

        public string stringValue
        {
            get => GetValueSafe<string>(eValueType.String);
            set => SetValueSafe(value, eValueType.String);
        }

        public bool boolValue
        {
            get => GetValueSafe<bool>(eValueType.Boolean);
            set => SetValueSafe(value, eValueType.Boolean);
        }
        #endregion

        public T GetValueSafe<T>(eValueType expectedType)
        {
            if (m_Type != expectedType)
            {
                return default; // Or throw an exception if you prefer strict type checking.
            }

            return GetValue<T>();
        }

        public void SetValueSafe<T>(T value, eValueType expectedType)
        {
            m_Type = expectedType;
            SetValue(value);
        }

        public T GetValue<T>()
        {
            if (m_Bytes == null || m_Bytes.Length == 0)
            {
                // For strings, MemoryPack might deserialize empty bytes to null.
                // If you prefer an empty string, handle it explicitly.
                if (typeof(T) == typeof(string)) return (T)(object)string.Empty;
                return default;
            }
            // The m_Type enum helps ensure you're deserializing to the correct type T.
            // The GetValueSafe<T> method already performs this check.
            return MemoryPackSerializer.Deserialize<T>(m_Bytes);
        }

        public void SetValue<T>(T value)
        {
            if (value == null)
            {
                // For reference types, MemoryPack can serialize null.
                // For value types, this path might not be hit unless T is nullable.
                m_Bytes = Array.Empty<byte>();
                return;
            }
            m_Bytes = MemoryPackSerializer.Serialize(value);
        }

        #region IComparable Implementation
        public int CompareTo(DynamicValue other)
        {
            if (m_Type != other.m_Type)
            {
                throw new InvalidOperationException($"Cannot compare different types: {m_Type} vs {other.m_Type}");
            }

            return CompareValues(m_Bytes, other.m_Bytes);
        }

        public int CompareTo(object obj)
        {
            if (obj is DynamicValue other)
            {
                return CompareTo(other);
            }
            throw new ArgumentException($"Object is not a {nameof(DynamicValue)}");
        }

        public bool Equals(DynamicValue other)
        {
            if (m_Type != other.m_Type) return false;
            return CompareValues(m_Bytes, other.m_Bytes) == 0;
        }

        static int CompareValues(byte[] bytes1, byte[] bytes2)
        {    
            // Handle null cases
            if (bytes1 == null && bytes2 == null) return 0;
            if (bytes1 == null) return -1;
            if (bytes2 == null) return 1;
            
            // Compare lengths first
            int lengthComparison = bytes1.Length.CompareTo(bytes2.Length);
            if (lengthComparison != 0) return lengthComparison;
            
            // Compare byte by byte
            for (int i = 0; i < bytes1.Length; i++)
            {
                int byteComparison = bytes1[i].CompareTo(bytes2[i]);
                if (byteComparison != 0) return byteComparison;
            }
            
            return 0; // Arrays are equal
        }
        #endregion

        #region Implicit & Explicit Conversions
        public static implicit operator DynamicValue(byte value) => new DynamicValue(value);
        public static implicit operator DynamicValue(sbyte value) => new DynamicValue(value);
        public static implicit operator DynamicValue(short value) => new DynamicValue(value);
        public static implicit operator DynamicValue(ushort value) => new DynamicValue(value);
        public static implicit operator DynamicValue(int value) => new DynamicValue(value);
        public static implicit operator DynamicValue(uint value) => new DynamicValue(value);
        public static implicit operator DynamicValue(long value) => new DynamicValue(value);
        public static implicit operator DynamicValue(ulong value) => new DynamicValue(value);
        public static implicit operator DynamicValue(float value) => new DynamicValue(value);
        public static implicit operator DynamicValue(double value) => new DynamicValue(value);
        public static implicit operator DynamicValue(string value) => new DynamicValue(value);
        public static implicit operator DynamicValue(bool value) => new DynamicValue(value);
        #endregion
    }
}
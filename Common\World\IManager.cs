
namespace GP.Common
{
    public interface IManager
    {
        void OnInitialize(World world);
        void OnWorldStateChanged(eWorldStatus status);
        void OnDestroy();
    }

    public abstract class Manager : IManager
    {
        public World World { get; private set; }
        protected DisposableBag m_EventBag;
        public virtual void OnInitialize(World world)
        {
            World = world;
            m_EventBag = new DisposableBag();
        }

        public virtual void OnDestroy()
        {
            m_EventBag.Dispose();
            World = null;
        }

        public virtual void OnWorldStateChanged(eWorldStatus status)
        {
        }

        public virtual void Tick() { }

        public void Subscribe<T>(Action<T> act) where T:IEvent
        {
            EventBus<T>.Subscribe(act).AddTo(m_EventBag);
        }

        public void Publish<T>(T evt) where T:IEvent
        {
            EventBus<T>.Publish(evt);
        }
    }
}

#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using GP.Common.Statescript;

namespace GP.Common.Statescript.Editor
{
    /// <summary>
    /// Main visual editor window for Statescript graphs
    /// </summary>
    public class StatescriptGraphEditor : EditorWindow
    {
        private StatescriptAsset _currentAsset;
        private StatescriptGraph _currentGraph;
        private StatescriptEditorData _editorData;
        private StatescriptEditorPreferences _preferences = new();

        // Editor state
        private Vector2 _panOffset = Vector2.zero;
        private float _zoomLevel = 1.0f;
        private bool _isDragging = false;
        private bool _isConnecting = false;
        private int _connectingFromNodeId = -1;
        private string _connectingFromPort = "";

        // Selection
        private HashSet<int> _selectedNodes = new();
        private HashSet<int> _selectedConnections = new();
        private Vector2 _selectionStart;
        private bool _isSelecting = false;

        // Context menu
        private Vector2 _contextMenuPosition;
        private bool _showContextMenu = false;

        // Auto-save
        private double _lastSaveTime;

        // GUI styles (cached)
        private GUIStyle _nodeStyle;
        private GUIStyle _selectedNodeStyle;
        private GUIStyle _nodeTitleStyle;
        private bool _stylesInitialized = false;

        [MenuItem("Window/Statescript/Graph Editor")]
        public static void OpenWindow()
        {
            var window = GetWindow<StatescriptGraphEditor>("Statescript Editor");
            window.Show();
        }

        public static void OpenAsset(StatescriptAsset asset)
        {
            var window = GetWindow<StatescriptGraphEditor>("Statescript Editor");
            window.LoadAsset(asset);
            window.Show();
            window.Focus();
        }

        private void OnEnable()
        {
            titleContent = new GUIContent("Statescript Editor", "Visual Statescript Graph Editor");
            minSize = new Vector2(800, 600);

            // Load preferences
            LoadPreferences();

            // Subscribe to selection changes
            Selection.selectionChanged += OnSelectionChanged;
        }

        private void OnDisable()
        {
            // Save preferences
            SavePreferences();

            // Unsubscribe from selection changes
            Selection.selectionChanged -= OnSelectionChanged;

            // Auto-save if needed
            if (_currentAsset != null && _preferences.AutoSave)
            {
                SaveCurrentGraph();
            }
        }

        private void OnSelectionChanged()
        {
            // Auto-load selected Statescript assets
            if (Selection.activeObject is StatescriptAsset asset)
            {
                LoadAsset(asset);
            }
        }

        private void OnGUI()
        {
            InitializeStyles();

            // Handle events first
            HandleEvents();

            // Draw background
            DrawBackground();

            // Apply zoom and pan
            var originalMatrix = GUI.matrix;
            var translation = Matrix4x4.TRS(_panOffset, Quaternion.identity, Vector3.one * _zoomLevel);
            GUI.matrix = translation;

            // Draw grid
            if (_preferences.ShowGrid)
            {
                DrawGrid();
            }

            // Draw graph content
            if (_currentGraph != null)
            {
                DrawConnections();
                DrawNodes();
            }

            // Restore matrix
            GUI.matrix = originalMatrix;

            // Draw UI overlay (not affected by zoom/pan)
            DrawToolbar();
            DrawStatusBar();

            // Draw context menu
            if (_showContextMenu)
            {
                DrawContextMenu();
            }

            // Handle auto-save
            HandleAutoSave();

            // Repaint if needed
            if (Event.current.type == EventType.MouseMove || _isDragging)
            {
                Repaint();
            }
        }

        private void InitializeStyles()
        {
            if (_stylesInitialized) return;

            _nodeStyle = new GUIStyle(GUI.skin.box)
            {
                normal = { background = MakeTexture(2, 2, new Color(0.3f, 0.3f, 0.3f, 0.8f)) },
                border = new RectOffset(2, 2, 2, 2),
                padding = new RectOffset(4, 4, 4, 4),
                alignment = TextAnchor.MiddleCenter
            };

            _selectedNodeStyle = new GUIStyle(_nodeStyle)
            {
                normal = { background = MakeTexture(2, 2, _preferences.SelectionColor) }
            };

            _nodeTitleStyle = new GUIStyle(GUI.skin.label)
            {
                alignment = TextAnchor.MiddleCenter,
                fontStyle = FontStyle.Bold,
                normal = { textColor = Color.white }
            };

            _stylesInitialized = true;
        }

        private Texture2D MakeTexture(int width, int height, Color color)
        {
            var pixels = new Color[width * height];
            for (int i = 0; i < pixels.Length; i++)
                pixels[i] = color;

            var texture = new Texture2D(width, height);
            texture.SetPixels(pixels);
            texture.Apply();
            return texture;
        }

        private void HandleEvents()
        {
            var e = Event.current;

            switch (e.type)
            {
                case EventType.MouseDown:
                    HandleMouseDown(e);
                    break;

                case EventType.MouseUp:
                    HandleMouseUp(e);
                    break;

                case EventType.MouseDrag:
                    HandleMouseDrag(e);
                    break;

                case EventType.ScrollWheel:
                    HandleScrollWheel(e);
                    break;

                case EventType.KeyDown:
                    HandleKeyDown(e);
                    break;

                case EventType.ContextClick:
                    HandleContextClick(e);
                    break;
            }
        }

        private void HandleMouseDown(Event e)
        {
            if (e.button == 0) // Left click
            {
                var worldPos = ScreenToWorldPosition(e.mousePosition);
                var clickedNode = GetNodeAtPosition(worldPos);

                if (clickedNode != null)
                {
                    // Node clicked
                    if (!e.control)
                    {
                        _selectedNodes.Clear();
                    }
                    _selectedNodes.Add(clickedNode.Id);
                    _isDragging = true;

                    // Double-click to open property editor
                    if (e.clickCount == 2)
                    {
                        StatescriptNodePropertyEditor.ShowEditor(clickedNode, _currentGraph);
                    }
                }
                else
                {
                    // Empty space clicked - start selection box
                    if (!e.control)
                    {
                        _selectedNodes.Clear();
                        _selectedConnections.Clear();
                    }
                    _selectionStart = worldPos;
                    _isSelecting = true;
                }

                e.Use();
            }
            else if (e.button == 2) // Middle click - pan
            {
                _isDragging = true;
                e.Use();
            }
        }

        private void HandleMouseUp(Event e)
        {
            _isDragging = false;
            _isSelecting = false;
            _isConnecting = false;
            e.Use();
        }

        private void HandleMouseDrag(Event e)
        {
            if (e.button == 0 && _selectedNodes.Count > 0) // Drag selected nodes
            {
                var delta = e.delta / _zoomLevel;
                foreach (var nodeId in _selectedNodes)
                {
                    var nodeData = _editorData?.GetNodeData(nodeId);
                    if (nodeData != null)
                    {
                        var newPos = nodeData.Position + delta;
                        nodeData.SetPosition(_preferences.SnapToGridIfEnabled(newPos));
                    }
                }
                MarkDirty();
            }
            else if (e.button == 2) // Pan view
            {
                _panOffset += e.delta;
                if (_editorData != null)
                {
                    _editorData.PanOffset = _panOffset;
                }
            }

            e.Use();
        }

        private void HandleScrollWheel(Event e)
        {
            var zoomDelta = -e.delta.y * _preferences.ZoomSpeed;
            var newZoom = Mathf.Clamp(_zoomLevel + zoomDelta, _preferences.MinZoom, _preferences.MaxZoom);

            if (newZoom != _zoomLevel)
            {
                // Zoom towards mouse position
                var mousePos = e.mousePosition;
                var worldPos = ScreenToWorldPosition(mousePos);

                _zoomLevel = newZoom;

                var newWorldPos = ScreenToWorldPosition(mousePos);
                _panOffset += (worldPos - newWorldPos) * _zoomLevel;

                if (_editorData != null)
                {
                    _editorData.ZoomLevel = _zoomLevel;
                    _editorData.PanOffset = _panOffset;
                }
            }

            e.Use();
        }

        private void HandleKeyDown(Event e)
        {
            switch (e.keyCode)
            {
                case KeyCode.Delete:
                    DeleteSelectedNodes();
                    e.Use();
                    break;

                case KeyCode.F:
                    FocusOnGraph();
                    e.Use();
                    break;

                case KeyCode.S when e.control:
                    SaveCurrentGraph();
                    e.Use();
                    break;
            }
        }

        private void HandleContextClick(Event e)
        {
            _contextMenuPosition = ScreenToWorldPosition(e.mousePosition);
            _showContextMenu = true;
            e.Use();
        }

        private Vector2 ScreenToWorldPosition(Vector2 screenPos)
        {
            return (screenPos - _panOffset) / _zoomLevel;
        }

        private Vector2 WorldToScreenPosition(Vector2 worldPos)
        {
            return worldPos * _zoomLevel + _panOffset;
        }

        private StatescriptNode GetNodeAtPosition(Vector2 worldPos)
        {
            if (_currentGraph == null || _editorData == null) return null;

            foreach (var node in _currentGraph.Nodes)
            {
                var nodeData = _editorData.GetNodeData(node.Id);
                if (nodeData != null && nodeData.Contains(worldPos))
                {
                    return node;
                }
            }
            return null;
        }

        private void LoadAsset(StatescriptAsset asset)
        {
            if (_currentAsset != null && _preferences.AutoSave)
            {
                SaveCurrentGraph();
            }

            _currentAsset = asset;
            _currentGraph = asset?.LoadGraph();
            _editorData = asset?.EditorData;

            if (_editorData != null)
            {
                _panOffset = _editorData.PanOffset;
                _zoomLevel = _editorData.ZoomLevel;

                // Ensure all nodes have editor data
                EnsureNodeEditorData();
            }

            _selectedNodes.Clear();
            _selectedConnections.Clear();

            Repaint();
        }

        private void EnsureNodeEditorData()
        {
            if (_currentGraph == null || _editorData == null) return;

            foreach (var node in _currentGraph.Nodes)
            {
                var nodeData = _editorData.GetNodeData(node.Id);
                if (nodeData == null)
                {
                    nodeData = new NodeEditorData
                    {
                        NodeId = node.Id,
                        Position = new Vector2(UnityEngine.Random.Range(100, 500), UnityEngine.Random.Range(100, 400)),
                        Size = GetDefaultNodeSize(node),
                        Color = _preferences.GetDefaultNodeColor(node.NodeType)
                    };
                    _editorData.SetNodeData(node.Id, nodeData);
                }
            }
        }

        private Vector2 GetDefaultNodeSize(StatescriptNode node)
        {
            return node.NodeType switch
            {
                StatescriptNodeType.Entry => new Vector2(100, 50),
                StatescriptNodeType.Action => new Vector2(120, 60),
                StatescriptNodeType.Condition => new Vector2(140, 70),
                StatescriptNodeType.Event => new Vector2(130, 65),
                StatescriptNodeType.Variable => new Vector2(110, 55),
                StatescriptNodeType.FlowControl => new Vector2(150, 80),
                _ => new Vector2(120, 60)
            };
        }

        private void SaveCurrentGraph()
        {
            if (_currentAsset != null && _currentGraph != null)
            {
                _currentAsset.SaveGraph(_currentGraph);
                _lastSaveTime = EditorApplication.timeSinceStartup;
                Debug.Log($"Saved Statescript graph: {_currentGraph.Name}");
            }
        }

        private void MarkDirty()
        {
            if (_currentAsset != null)
            {
                EditorUtility.SetDirty(_currentAsset);
            }
        }

        private void LoadPreferences()
        {
            // Load from EditorPrefs or use defaults
            _preferences.GridSize = EditorPrefs.GetFloat("StatescriptEditor.GridSize", 20f);
            _preferences.SnapToGrid = EditorPrefs.GetBool("StatescriptEditor.SnapToGrid", true);
            _preferences.AutoSave = EditorPrefs.GetBool("StatescriptEditor.AutoSave", true);
            _preferences.AutoSaveInterval = EditorPrefs.GetFloat("StatescriptEditor.AutoSaveInterval", 30f);
        }

        private void SavePreferences()
        {
            EditorPrefs.SetFloat("StatescriptEditor.GridSize", _preferences.GridSize);
            EditorPrefs.SetBool("StatescriptEditor.SnapToGrid", _preferences.SnapToGrid);
            EditorPrefs.SetBool("StatescriptEditor.AutoSave", _preferences.AutoSave);
            EditorPrefs.SetFloat("StatescriptEditor.AutoSaveInterval", _preferences.AutoSaveInterval);
        }

        private void HandleAutoSave()
        {
            if (_preferences.AutoSave && _currentAsset != null)
            {
                var timeSinceLastSave = EditorApplication.timeSinceStartup - _lastSaveTime;
                if (timeSinceLastSave > _preferences.AutoSaveInterval)
                {
                    SaveCurrentGraph();
                }
            }
        }

        private void DeleteSelectedNodes()
        {
            if (_currentGraph == null || _selectedNodes.Count == 0) return;

            foreach (var nodeId in _selectedNodes)
            {
                _currentGraph.RemoveNode(nodeId);
                _editorData?.RemoveNodeData(nodeId);
            }

            _selectedNodes.Clear();
            MarkDirty();
        }

        private void FocusOnGraph()
        {
            if (_currentGraph == null || _currentGraph.Nodes.Count == 0) return;

            // Calculate bounds of all nodes
            var bounds = new Rect();
            bool first = true;

            foreach (var node in _currentGraph.Nodes)
            {
                var nodeData = _editorData?.GetNodeData(node.Id);
                if (nodeData != null)
                {
                    var nodeRect = nodeData.GetRect();
                    if (first)
                    {
                        bounds = nodeRect;
                        first = false;
                    }
                    else
                    {
                        bounds = RectUtils.Encapsulate(bounds, nodeRect);
                    }
                }
            }

            if (!first)
            {
                // Center the view on the bounds
                var center = bounds.center;
                var windowCenter = position.size * 0.5f;
                _panOffset = windowCenter - center * _zoomLevel;

                if (_editorData != null)
                {
                    _editorData.PanOffset = _panOffset;
                }
            }
        }

        private void DrawBackground()
        {
            EditorGUI.DrawRect(new Rect(0, 0, position.width, position.height), _preferences.BackgroundColor);
        }

        private void DrawGrid()
        {
            var gridSize = _preferences.GridSize * _zoomLevel;
            var gridColor = _preferences.GridColor;

            var offsetX = _panOffset.x % gridSize;
            var offsetY = _panOffset.y % gridSize;

            // Vertical lines
            for (float x = offsetX; x < position.width; x += gridSize)
            {
                EditorGUI.DrawRect(new Rect(x, 0, 1, position.height), gridColor);
            }

            // Horizontal lines
            for (float y = offsetY; y < position.height; y += gridSize)
            {
                EditorGUI.DrawRect(new Rect(0, y, position.width, 1), gridColor);
            }
        }

        private void DrawNodes()
        {
            if (_currentGraph == null || _editorData == null) return;

            foreach (var node in _currentGraph.Nodes)
            {
                DrawNode(node);
            }
        }

        private void DrawNode(StatescriptNode node)
        {
            var nodeData = _editorData.GetNodeData(node.Id);
            if (nodeData == null) return;

            var rect = nodeData.GetRect();
            var isSelected = _selectedNodes.Contains(node.Id);

            // Draw node background
            var style = isSelected ? _selectedNodeStyle : _nodeStyle;
            var originalColor = GUI.backgroundColor;
            GUI.backgroundColor = nodeData.Color;

            GUI.Box(rect, "", style);
            GUI.backgroundColor = originalColor;

            // Draw node title
            var titleRect = new Rect(rect.x, rect.y + 2, rect.width, 20);
            GUI.Label(titleRect, node.Name, _nodeTitleStyle);

            // Draw node type
            var typeRect = new Rect(rect.x, rect.y + 22, rect.width, 15);
            var typeStyle = new GUIStyle(GUI.skin.label)
            {
                alignment = TextAnchor.MiddleCenter,
                fontSize = 10,
                normal = { textColor = Color.gray }
            };
            GUI.Label(typeRect, node.NodeType.ToString(), typeStyle);

            // Draw node state indicator (if running)
            if (node.IsActive)
            {
                var stateRect = new Rect(rect.xMax - 15, rect.y + 2, 12, 12);
                var stateColor = node.State switch
                {
                    StatescriptNodeState.Running => Color.yellow,
                    StatescriptNodeState.Success => Color.green,
                    StatescriptNodeState.Failure => Color.red,
                    StatescriptNodeState.Waiting => Color.cyan,
                    _ => Color.gray
                };
                EditorGUI.DrawRect(stateRect, stateColor);
            }

            // Draw connection points
            DrawNodeConnectionPoints(node, rect);
        }

        private void DrawNodeConnectionPoints(StatescriptNode node, Rect nodeRect)
        {
            var pointSize = 8f;
            var pointColor = Color.white;
            var e = Event.current;

            // Input point (left side)
            if (node.NodeType != StatescriptNodeType.Entry)
            {
                var inputPoint = new Rect(
                    nodeRect.x - pointSize * 0.5f,
                    nodeRect.center.y - pointSize * 0.5f,
                    pointSize,
                    pointSize
                );
                EditorGUI.DrawRect(inputPoint, pointColor);

                // Handle input point clicks
                if (e.type == EventType.MouseDown && e.button == 0 && inputPoint.Contains(e.mousePosition))
                {
                    if (_isConnecting && _connectingFromNodeId != node.Id)
                    {
                        // Complete connection
                        CreateConnection(_connectingFromNodeId, node.Id, _connectingFromPort, "In");
                        _isConnecting = false;
                        _connectingFromNodeId = -1;
                        e.Use();
                    }
                }
            }

            // Output point (right side)
            var outputPoint = new Rect(
                nodeRect.xMax - pointSize * 0.5f,
                nodeRect.center.y - pointSize * 0.5f,
                pointSize,
                pointSize
            );
            EditorGUI.DrawRect(outputPoint, pointColor);

            // Handle output point clicks
            if (e.type == EventType.MouseDown && e.button == 0 && outputPoint.Contains(e.mousePosition))
            {
                if (!_isConnecting)
                {
                    // Start connection
                    _isConnecting = true;
                    _connectingFromNodeId = node.Id;
                    _connectingFromPort = "Out";
                    e.Use();
                }
            }
        }

        private void DrawConnections()
        {
            if (_currentGraph == null || _editorData == null) return;

            foreach (var connection in _currentGraph.Connections)
            {
                DrawConnection(connection);
            }

            // Draw connection being created
            if (_isConnecting && _connectingFromNodeId >= 0)
            {
                DrawConnectionInProgress();
            }
        }

        private void DrawConnection(StatescriptConnection connection)
        {
            var fromNode = _currentGraph.GetNode(connection.FromNodeId);
            var toNode = _currentGraph.GetNode(connection.ToNodeId);

            if (fromNode == null || toNode == null) return;

            var fromNodeData = _editorData.GetNodeData(connection.FromNodeId);
            var toNodeData = _editorData.GetNodeData(connection.ToNodeId);

            if (fromNodeData == null || toNodeData == null) return;

            var fromRect = fromNodeData.GetRect();
            var toRect = toNodeData.GetRect();

            var startPos = new Vector2(fromRect.xMax, fromRect.center.y);
            var endPos = new Vector2(toRect.x, toRect.center.y);

            var connectionData = _editorData.GetConnectionData(connection.Id);
            var connectionColor = connectionData?.Color ?? Color.white;
            var connectionWidth = connectionData?.Width ?? 2f;

            // Draw bezier curve
            var bezierPoints = connectionData?.GetBezierPoints(startPos, endPos) ??
                              GetDefaultBezierPoints(startPos, endPos);

            Handles.DrawBezier(
                bezierPoints[0], bezierPoints[3],
                bezierPoints[1], bezierPoints[2],
                connectionColor, null, connectionWidth
            );

            // Draw arrow at end
            DrawConnectionArrow(endPos, startPos, connectionColor);

            // Draw connection label if any
            if (connectionData != null && connectionData.ShowLabel && !string.IsNullOrEmpty(connectionData.Label))
            {
                var labelPos = Vector2.Lerp(startPos, endPos, 0.5f);
                var labelRect = new Rect(labelPos.x - 30, labelPos.y - 10, 60, 20);
                GUI.Label(labelRect, connectionData.Label, EditorStyles.centeredGreyMiniLabel);
            }
        }

        private Vector2[] GetDefaultBezierPoints(Vector2 startPos, Vector2 endPos)
        {
            var distance = Vector2.Distance(startPos, endPos);
            var offset = Mathf.Min(distance * 0.5f, 100f);

            var startControl = startPos + Vector2.right * offset;
            var endControl = endPos + Vector2.left * offset;

            return new Vector2[] { startPos, startControl, endControl, endPos };
        }

        private void DrawConnectionArrow(Vector2 endPos, Vector2 startPos, Color color)
        {
            var direction = (endPos - startPos).normalized;
            var arrowSize = 8f;

            var arrowPoint1 = endPos - direction * arrowSize + Vector2.Perpendicular(direction) * arrowSize * 0.5f;
            var arrowPoint2 = endPos - direction * arrowSize - Vector2.Perpendicular(direction) * arrowSize * 0.5f;

            Handles.color = color;
            Handles.DrawLine(endPos, arrowPoint1);
            Handles.DrawLine(endPos, arrowPoint2);
            Handles.color = Color.white;
        }

        private void DrawToolbar()
        {
            var toolbarRect = new Rect(0, 0, position.width, 20);
            GUI.Box(toolbarRect, "", EditorStyles.toolbar);

            GUILayout.BeginArea(toolbarRect);
            GUILayout.BeginHorizontal();

            // Asset info
            var assetName = _currentAsset != null ? _currentAsset.name : "No Asset";
            GUILayout.Label($"Asset: {assetName}", EditorStyles.toolbarButton, GUILayout.Width(150));

            GUILayout.FlexibleSpace();

            // Zoom controls
            GUILayout.Label($"Zoom: {_zoomLevel:F1}x", EditorStyles.toolbarButton, GUILayout.Width(80));

            if (GUILayout.Button("Fit", EditorStyles.toolbarButton, GUILayout.Width(30)))
            {
                FocusOnGraph();
            }

            // Grid toggle
            var newShowGrid = GUILayout.Toggle(_preferences.ShowGrid, "Grid", EditorStyles.toolbarButton);
            if (newShowGrid != _preferences.ShowGrid)
            {
                _preferences.ShowGrid = newShowGrid;
            }

            // Save button
            if (GUILayout.Button("Save", EditorStyles.toolbarButton, GUILayout.Width(40)))
            {
                SaveCurrentGraph();
            }

            GUILayout.EndHorizontal();
            GUILayout.EndArea();
        }

        private void DrawStatusBar()
        {
            var statusRect = new Rect(0, position.height - 20, position.width, 20);
            GUI.Box(statusRect, "", EditorStyles.toolbar);

            GUILayout.BeginArea(statusRect);
            GUILayout.BeginHorizontal();

            // Graph stats
            if (_currentGraph != null)
            {
                GUILayout.Label($"Nodes: {_currentGraph.Nodes.Count}", EditorStyles.toolbarButton);
                GUILayout.Label($"Connections: {_currentGraph.Connections.Count}", EditorStyles.toolbarButton);
                GUILayout.Label($"Variables: {_currentGraph.Variables.Count}", EditorStyles.toolbarButton);
            }

            GUILayout.FlexibleSpace();

            // Selection info
            if (_selectedNodes.Count > 0)
            {
                GUILayout.Label($"Selected: {_selectedNodes.Count} nodes", EditorStyles.toolbarButton);
            }

            GUILayout.EndHorizontal();
            GUILayout.EndArea();
        }

        private void DrawContextMenu()
        {
            var menuRect = new Rect(_contextMenuPosition.x, _contextMenuPosition.y, 200, 150);
            GUI.Box(menuRect, "", GUI.skin.box);

            GUILayout.BeginArea(menuRect);
            GUILayout.BeginVertical();

            GUILayout.Label("Add Node", EditorStyles.boldLabel);

            if (GUILayout.Button("Entry Node"))
            {
                CreateNode<EntryNode>();
                _showContextMenu = false;
            }

            if (GUILayout.Button("Log Node"))
            {
                CreateNode<LogNode>();
                _showContextMenu = false;
            }

            if (GUILayout.Button("Wait Node"))
            {
                CreateNode<WaitNode>();
                _showContextMenu = false;
            }

            if (GUILayout.Button("Branch Node"))
            {
                CreateNode<BranchNode>();
                _showContextMenu = false;
            }

            if (GUILayout.Button("Sequence Node"))
            {
                CreateNode<SequenceNode>();
                _showContextMenu = false;
            }

            GUILayout.EndVertical();
            GUILayout.EndArea();

            // Close menu if clicked outside
            if (Event.current.type == EventType.MouseDown && !menuRect.Contains(Event.current.mousePosition))
            {
                _showContextMenu = false;
            }
        }

        private void CreateNode<T>() where T : StatescriptNode, new()
        {
            if (_currentGraph == null || _editorData == null) return;

            var node = new T();
            node.Id = GetNextNodeId();
            node.Name = typeof(T).Name.Replace("Node", "");

            _currentGraph.AddNode(node);

            var nodeData = new NodeEditorData
            {
                NodeId = node.Id,
                Position = _preferences.SnapToGridIfEnabled(_contextMenuPosition),
                Size = GetDefaultNodeSize(node),
                Color = _preferences.GetDefaultNodeColor(node.NodeType)
            };

            _editorData.SetNodeData(node.Id, nodeData);
            MarkDirty();
        }

        private int GetNextNodeId()
        {
            if (_currentGraph == null) return 1;

            var maxId = 0;
            foreach (var node in _currentGraph.Nodes)
            {
                if (node.Id > maxId)
                    maxId = node.Id;
            }
            return maxId + 1;
        }

        private void CreateConnection(int fromNodeId, int toNodeId, string fromPort, string toPort)
        {
            if (_currentGraph == null || _editorData == null) return;

            var connection = new StatescriptConnection(fromNodeId, toNodeId, fromPort, toPort);
            connection.Id = GetNextConnectionId();

            _currentGraph.AddConnection(connection);

            var connectionData = new ConnectionEditorData
            {
                ConnectionId = connection.Id,
                Color = Color.white,
                Width = 2f
            };

            _editorData.SetConnectionData(connection.Id, connectionData);
            MarkDirty();
        }

        private int GetNextConnectionId()
        {
            if (_currentGraph == null) return 1;

            var maxId = 0;
            foreach (var connection in _currentGraph.Connections)
            {
                if (connection.Id > maxId)
                    maxId = connection.Id;
            }
            return maxId + 1;
        }

        private void DrawConnectionInProgress()
        {
            var fromNode = _currentGraph.GetNode(_connectingFromNodeId);
            if (fromNode == null) return;

            var fromNodeData = _editorData.GetNodeData(_connectingFromNodeId);
            if (fromNodeData == null) return;

            var fromRect = fromNodeData.GetRect();
            var startPos = new Vector2(fromRect.xMax, fromRect.center.y);
            var endPos = ScreenToWorldPosition(Event.current.mousePosition);

            var bezierPoints = GetDefaultBezierPoints(startPos, endPos);

            Handles.DrawBezier(
                bezierPoints[0], bezierPoints[3],
                bezierPoints[1], bezierPoints[2],
                Color.yellow, null, 3f
            );

            Repaint();
        }
    }

    /// <summary>
    /// Utility class for Rect operations
    /// </summary>
    public static class RectUtils
    {
        public static Rect Encapsulate(Rect rect, Rect other)
        {
            var xMin = Mathf.Min(rect.xMin, other.xMin);
            var yMin = Mathf.Min(rect.yMin, other.yMin);
            var xMax = Mathf.Max(rect.xMax, other.xMax);
            var yMax = Mathf.Max(rect.yMax, other.yMax);

            return new Rect(xMin, yMin, xMax - xMin, yMax - yMin);
        }
    }
}
#endif

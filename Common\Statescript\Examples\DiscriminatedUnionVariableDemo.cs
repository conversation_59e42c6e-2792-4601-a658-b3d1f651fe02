using System;
using GP.Common.Statescript;
using GP.Common.Statescript.Serialization;

namespace GP.Common.Statescript.Examples
{
    /// <summary>
    /// Demonstration of the new discriminated union variable system
    /// </summary>
    public static class DiscriminatedUnionVariableDemo
    {
        /// <summary>
        /// Run comprehensive tests of the new variable system
        /// </summary>
        public static void RunDemo()
        {
            Console.WriteLine("=== Discriminated Union Variable System Demo ===");
            Console.WriteLine();

            DemonstrateTypeSpecificCreation();
            DemonstrateFactoryPattern();
            DemonstrateSerializationBenefits();
            DemonstratePerformanceBenefits();
            DemonstratePolymorphicBehavior();

            Console.WriteLine("Demo completed successfully!");
        }

        private static void DemonstrateTypeSpecificCreation()
        {
            Console.WriteLine("1. Type-Specific Variable Creation:");
            Console.WriteLine("   Each variable type is its own class with compile-time safety");
            Console.WriteLine();

            // Direct instantiation with type safety
            var boolVar = new BooleanVariable("IsActive", true);
            var intVar = new IntegerVariable("Health", 100);
            var floatVar = new FloatVariable("Speed", 5.5f);
            var stringVar = new StringVariable("PlayerName", "Hero");
            var vec2Var = new Vector2Variable("Position2D", new FVector2(Fix64.one, Fix64.one * 2));
            var vec3Var = new Vector3Variable("Position3D", new FVector3(Fix64.one, Fix64.one * 2, Fix64.one * 3));
            var entityVar = new EntityVariable("TargetEntity", 12345);
            var objectVar = new ObjectVariable("CustomData", "Some custom data");

            Console.WriteLine($"   {boolVar}");
            Console.WriteLine($"   {intVar}");
            Console.WriteLine($"   {floatVar}");
            Console.WriteLine($"   {stringVar}");
            Console.WriteLine($"   {vec2Var}");
            Console.WriteLine($"   {vec3Var}");
            Console.WriteLine($"   {entityVar}");
            Console.WriteLine($"   {objectVar}");
            Console.WriteLine();
        }

        private static void DemonstrateFactoryPattern()
        {
            Console.WriteLine("2. Factory Pattern Usage:");
            Console.WriteLine("   Centralized creation with type registration");
            Console.WriteLine();

            // Factory creation methods
            var variables = new StatescriptVariable[]
            {
                StatescriptVariableFactory.Create("GenericBool", StatescriptVariableType.Boolean),
                StatescriptVariableFactory.CreateInteger("FactoryInt", 42),
                StatescriptVariableFactory.CreateFloat("FactoryFloat", 3.14f),
                StatescriptVariableFactory.CreateString("FactoryString", "Factory Created"),
                StatescriptVariableFactory.Create<BooleanVariable>("GenericTyped")
            };

            foreach (var variable in variables)
            {
                Console.WriteLine($"   Created: {variable}");
            }

            // Show supported types
            Console.WriteLine($"   Supported types: {string.Join(", ", StatescriptVariableFactory.GetSupportedTypes())}");
            Console.WriteLine();
        }

        private static void DemonstrateSerializationBenefits()
        {
            Console.WriteLine("3. Serialization Benefits:");
            Console.WriteLine("   No more MemoryPack errors, efficient union serialization");
            Console.WriteLine();

            var graph = new StatescriptGraph("Serialization Test");
            
            // Add various variable types
            graph.Variables.Add(StatescriptVariableFactory.CreateBoolean("TestBool", true));
            graph.Variables.Add(StatescriptVariableFactory.CreateInteger("TestInt", 42));
            graph.Variables.Add(StatescriptVariableFactory.CreateFloat("TestFloat", 3.14f));
            graph.Variables.Add(StatescriptVariableFactory.CreateString("TestString", "Hello World"));
            graph.Variables.Add(StatescriptVariableFactory.CreateVector2("TestVec2", new FVector2(Fix64.one, Fix64.one * 2)));

            try
            {
                // Test all serialization formats
                var jsonData = StatescriptSerializer.Serialize(graph, StatescriptSerializationFormat.Json);
                var binaryData = StatescriptSerializer.Serialize(graph, StatescriptSerializationFormat.Binary);
                var compressedData = StatescriptSerializer.Serialize(graph, StatescriptSerializationFormat.CompressedBinary);

                Console.WriteLine($"   ✅ JSON serialization: {jsonData.Length} bytes");
                Console.WriteLine($"   ✅ Binary serialization: {binaryData.Length} bytes");
                Console.WriteLine($"   ✅ Compressed serialization: {compressedData.Length} bytes");

                // Test deserialization
                var deserializedGraph = StatescriptSerializer.Deserialize(binaryData, StatescriptSerializationFormat.Binary);
                Console.WriteLine($"   ✅ Deserialization: {deserializedGraph.Variables.Count} variables restored");

                // Verify variable types are preserved
                foreach (var variable in deserializedGraph.Variables)
                {
                    Console.WriteLine($"      {variable.GetType().Name}: {variable}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Serialization failed: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static void DemonstratePerformanceBenefits()
        {
            Console.WriteLine("4. Performance Benefits:");
            Console.WriteLine("   No boxing/unboxing, direct memory access");
            Console.WriteLine();

            var intVar = StatescriptVariableFactory.CreateInteger("PerformanceTest", 42);
            var floatVar = StatescriptVariableFactory.CreateFloat("FloatTest", 3.14f);
            var boolVar = StatescriptVariableFactory.CreateBoolean("BoolTest", true);

            // Demonstrate direct access without boxing
            Console.WriteLine("   Direct access (no boxing):");
            var startTime = DateTime.UtcNow;
            
            for (int i = 0; i < 100000; i++)
            {
                // Direct access - no boxing
                var intValue = intVar.GetValue<int>();
                var floatValue = floatVar.GetValue<float>();
                var boolValue = boolVar.GetValue<bool>();
                
                // Direct setting - no boxing
                intVar.SetValue(intValue + 1);
                floatVar.SetValue(floatValue + 0.1f);
                boolVar.SetValue(!boolValue);
            }
            
            var elapsed = DateTime.UtcNow - startTime;
            Console.WriteLine($"   100,000 operations completed in {elapsed.TotalMilliseconds:F2}ms");
            Console.WriteLine($"   Final values: int={intVar.GetValue<int>()}, float={floatVar.GetValue<float>():F2}, bool={boolVar.GetValue<bool>()}");
            Console.WriteLine();
        }

        private static void DemonstratePolymorphicBehavior()
        {
            Console.WriteLine("5. Polymorphic Behavior:");
            Console.WriteLine("   Each type can have specialized behavior");
            Console.WriteLine();

            var variables = new StatescriptVariable[]
            {
                StatescriptVariableFactory.CreateBoolean("PolymorphicBool", false),
                StatescriptVariableFactory.CreateInteger("PolymorphicInt", 0),
                StatescriptVariableFactory.CreateFloat("PolymorphicFloat", 0f),
                StatescriptVariableFactory.CreateString("PolymorphicString", ""),
                StatescriptVariableFactory.CreateVector2("PolymorphicVec2", FVector2.zero)
            };

            Console.WriteLine("   Setting different value types on each variable:");
            foreach (var variable in variables)
            {
                Console.WriteLine($"   Before: {variable}");
                
                // Try setting different types - each variable handles conversion differently
                switch (variable)
                {
                    case BooleanVariable boolVar:
                        boolVar.SetValue("true"); // String to bool conversion
                        break;
                    case IntegerVariable intVar:
                        intVar.SetValue(3.14f); // Float to int conversion
                        break;
                    case FloatVariable floatVar:
                        floatVar.SetValue(42); // Int to float conversion
                        break;
                    case StringVariable stringVar:
                        stringVar.SetValue(123); // Any type to string conversion
                        break;
                    case Vector2Variable vec2Var:
                        vec2Var.SetValue(new FVector2(Fix64.one * 5, Fix64.one * 10));
                        break;
                }
                
                Console.WriteLine($"   After:  {variable}");
            }

            Console.WriteLine();
            Console.WriteLine("   Resetting all variables to defaults:");
            foreach (var variable in variables)
            {
                variable.ResetToDefault();
                Console.WriteLine($"   Reset:  {variable}");
            }

            Console.WriteLine();
        }
    }
}
